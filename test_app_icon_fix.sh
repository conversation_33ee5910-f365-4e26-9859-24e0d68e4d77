#!/bin/bash

# ZenTomato 关于页面App图标修复测试脚本
# 测试使用NSApp.applicationIconImage获取系统应用图标的修复效果

echo "🍅 ZenTomato 关于页面App图标修复测试"
echo "========================================"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "测试 $TOTAL_TESTS: $test_name ... "
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo "✅ 通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ 失败"
    fi
}

# 辅助函数：检查代码是否包含指定内容
check_code_contains() {
    local file_path="$1"
    local search_pattern="$2"
    
    if [ -f "$file_path" ]; then
        grep -q "$search_pattern" "$file_path"
    else
        return 1
    fi
}

# 辅助函数：检查代码是否不包含指定内容
check_code_not_contains() {
    local file_path="$1"
    local search_pattern="$2"
    
    if [ -f "$file_path" ]; then
        ! grep -q "$search_pattern" "$file_path"
    else
        return 1
    fi
}

echo "开始执行测试..."
echo ""

# 测试1: 检查AboutView文件是否存在
run_test "AboutView.swift 文件存在" "[ -f 'ZenTomato/Views/AboutView.swift' ]"

# 测试2: 检查是否有getAppIcon方法
run_test "包含 getAppIcon 方法" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'getAppIcon()'"

# 测试3: 检查是否有appIconView方法
run_test "包含 appIconView 方法" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'appIconView'"

# 测试4: 检查是否有多层回退机制
run_test "包含多层图标回退机制" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'NSApp.applicationIconImage'"

# 测试5: 检查是否保留了Bundle图标回退
run_test "保留 Bundle 图标回退" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'NSImage(named: \"AppIcon\")'"

# 测试13: 检查是否有图标尺寸调整
run_test "包含图标尺寸调整" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'targetSize = NSSize'"

# 测试6: 检查是否保留了系统图标最终回退
run_test "保留系统图标最终回退" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'leaf.fill'"

# 测试7: 检查圆角样式是否保持一致
run_test "圆角样式保持一致" "check_code_contains 'ZenTomato/Views/AboutView.swift' '80 \* 0.2237'"

# 测试8: 检查阴影效果是否保持
run_test "阴影效果保持" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'shadow.*Color.black.opacity.*radius.*4'"

# 测试9: 检查应用是否能成功编译
run_test "应用编译成功" "xcodebuild -project ZenTomato.xcodeproj -scheme ZenTomato -configuration Debug build -quiet"

# 测试10: 检查应用图标资源是否存在
run_test "应用图标资源存在" "[ -d 'ZenTomato/Assets.xcassets/AppIcon.appiconset' ]"

# 测试11: 检查图标文件是否完整
run_test "图标文件完整" "[ -f 'ZenTomato/Assets.xcassets/AppIcon.appiconset/icon_128x128.png' ]"

# 测试12: 检查MainView中的图标加载是否正常
run_test "MainView 图标加载正常" "check_code_contains 'ZenTomato/Views/MainView.swift' 'NSImage(named: \"AppIcon\")'"

# 测试14: 检查窗口高度是否已调整
run_test "窗口高度已调整为420" "check_code_contains 'ZenTomato/ViewModels/AboutWindowManager.swift' 'height: 420'"

# 测试15: 检查视图高度是否已调整
run_test "视图高度已调整为420" "check_code_contains 'ZenTomato/Views/AboutView.swift' 'height: 420'"

echo ""
echo "========================================"
echo "测试完成！"
echo "总测试数: $TOTAL_TESTS"
echo "通过测试: $PASSED_TESTS"
echo "失败测试: $((TOTAL_TESTS - PASSED_TESTS))"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo "🎉 所有测试通过！关于页面App图标修复成功！"
    exit 0
else
    echo "⚠️  有测试失败，请检查相关问题。"
    exit 1
fi
